import { HttpError } from "@/shared/key-management/error";

import { type <PERSON><PERSON> } from "@/lib/middlewares/create-proxy";

import { handleBlockedResponse } from "./blocked-handler";

export const handleStreamedResponse: Handler = async ({ ctx, response }) => {
  const contentType = response.headers.get("Content-Type");

  if (!response.body) {
    throw new HttpError(500, "Upstream error: No response body");
  }

  if (contentType?.startsWith("application/json")) {
    ctx.var.logger.warn(
      { contentType },
      "Upstream returned JSON response instead of SSE. Falling back to blocked response.",
    );
    return handleBlockedResponse({ ctx, response });
  }

  if (!contentType?.startsWith("text/event-stream")) {
    throw new HttpError(500, `Upstream error: Expected SSE response. Got: ${contentType}`);
  }

  // Handle stream response
};
