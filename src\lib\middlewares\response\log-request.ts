import { schema } from "@/shared/database";

import type { ResponseBody } from "@/lib/schemas";

import type { Middleware } from "../create-proxy";

export const logRequest: Middleware = async ({ ctx }) => {
  const requestData = ctx.var.requestData!;
  const body = ctx.var.fullResponseBody;
  let usages = { inputTokens: 0, outputTokens: 0, reasoningTokens: 0 };

  if (!ctx.res.ok || !body) return;

  switch (true) {
    case requestData.provider === "gemini" && "usageMetadata" in body: {
      usages = {
        inputTokens: body.usageMetadata.promptTokenCount,
        outputTokens: body.usageMetadata.candidatesTokenCount,
        reasoningTokens: body.usageMetadata.thoughtsTokenCount ?? 0,
      };
      break;
    }

    case requestData.provider === "deepseek" && "usage" in body: {
      usages = {
        inputTokens: body.usage?.prompt_tokens ?? 0,
        outputTokens: body.usage?.completion_tokens ?? 0,
        reasoningTokens: body.usage?.completion_tokens_details?.reasoning_tokens ?? 0,
      };
      break;
    }

    case requestData.provider === "openai" && "usage" in body: {
      usages = {
        inputTokens: body.usage?.prompt_tokens ?? 0,
        outputTokens: body.usage?.completion_tokens ?? 0,
        reasoningTokens: body.usage?.completion_tokens_details?.reasoning_tokens ?? 0,
      };
      break;
    }
  }

  ctx.var.afterResponsePromises.push(
    ctx.var.db.insert(schema.logs).values({
      provider: requestData.provider,
      model: requestData.model,
      modelFamilyId: requestData.family!.id,
      keyHash: requestData.selectedKey!.hash,
      userId: "univnrdhsfj5r3yin9co4kul",

      inputTokens: usages.inputTokens,
      outputTokens: usages.outputTokens,
      reasoningTokens: usages.reasoningTokens,
    }),
  );

  return;
};
