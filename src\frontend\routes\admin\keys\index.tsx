import { useMutation, useQ<PERSON>y, useQueryClient } from "@tanstack/react-query";
import {
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  type ColumnDef,
  type PaginationState,
  type SortingState,
  type VisibilityState,
} from "@tanstack/react-table";

import { CheckCircleIcon, KeyIcon, PlusIcon, RefreshCwIcon, SearchIcon } from "lucide-react";
import { useCallback, useMemo, useState } from "react";
import { NavLink } from "react-router";
import { toast } from "sonner";

import { DataTable } from "@/components/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Icons } from "@/components/ui/icons";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { AddKeyDialog } from "@/components/admin/add-key-dialog";
import { KeyActions } from "@/components/admin/key-actions";
import { KeyStatus } from "@/components/admin/key-status";
import { DataTableColumnHeader } from "@/components/table/data-table-column-header";
import { DataTableViewOptions } from "@/components/table/data-table-view-options";

import { useKeysCache } from "@/frontend/hooks/use-keys-cache";

import type { Key } from "@/shared/key-management";
import { LLM_PROVIDERS, LLM_PROVIDER_DISPLAY_NAME, type LLM_Providers } from "@/shared/providers";
import { format } from "@/shared/utils";

import { useTRPC } from "@/lib/trpc/client";

type KeyStatus = Key["status"] | "all";
type RecheckStatus = "all" | "out_of_quota" | "unknown";

function getColumns(refetch: () => void): ColumnDef<Key>[] {
  return [
    {
      accessorKey: "hash",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Key ID" />,
      cell: ({ getValue }) => {
        return (
          <NavLink to={`/admin/keys/${getValue()}`} className="font-mono text-sm">
            <span className="underline-offset-4 hover:underline">
              {getValue<string>().slice(0, 12)}
            </span>
            ...
          </NavLink>
        );
      },
      enableSorting: true,
      enableHiding: false,
    },
    {
      accessorKey: "provider",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Provider" />,
      cell: ({ getValue }) => {
        const provider = getValue<LLM_Providers>();

        return (
          <div className="flex items-center gap-2">
            <Icons.provider provider={provider} className="size-4" />
            <span>{LLM_PROVIDER_DISPLAY_NAME[provider]}</span>
          </div>
        );
      },
      enableSorting: true,
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "status",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
      cell: ({ row }) => {
        return <KeyStatus keyData={row.original} />;
      },
      enableSorting: true,
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "modelIds",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Models" />,
      cell: ({ getValue }) => {
        const modelIds = getValue() as string[];
        return (
          <div className="text-muted-foreground text-sm">
            {modelIds.length > 0 ? `${modelIds.length} models` : "No models"}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "lastUsedAt",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Last Used" />,
      cell: ({ getValue }) => {
        const timestamp = getValue() as number;
        return (
          <div className="text-muted-foreground text-sm">
            {timestamp > 0 ? format.date(timestamp) : "Never"}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Created" />,
      cell: ({ getValue }) => (
        <div className="text-muted-foreground text-sm">
          {format.date(getValue<number>() * 1000)}
        </div>
      ),
      enableSorting: true,
    },
    {
      accessorKey: "lastCheckedAt",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Last Checked" />,
      cell: ({ getValue }) => {
        const timestamp = getValue() as number;
        return (
          <div className="text-muted-foreground text-sm">
            {timestamp > 0 ? format.date(timestamp) : "None"}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "nextCheckAt",
      header: ({ column }) => <DataTableColumnHeader column={column} title="Next Check" />,
      cell: ({ getValue }) => {
        const timestamp = getValue() as number;
        return (
          <div className="text-muted-foreground text-sm">
            {timestamp > 0 ? format.date(timestamp) : "None"}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => <KeyActions keyData={row.original} refetch={refetch} />,
      enableHiding: false,
    },
  ];
}

export function AdminKeysPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedProvider, setSelectedProvider] = useState<LLM_Providers | "all">("all");
  const [selectedStatus, setSelectedStatus] = useState<KeyStatus>("all");

  const [isAddKeyDialogOpen, setIsAddKeyDialogOpen] = useState(false);

  const [isRecheckDialogOpen, setIsRecheckDialogOpen] = useState(false);
  const [selectedRecheckStatus, setSelectedRecheckStatus] = useState<RecheckStatus>("all");

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [pagination, setPagination] = useState<PaginationState>({ pageIndex: 0, pageSize: 10 });

  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const { data: freshKeys, isLoading, refetch } = useQuery(trpc.keys.list.queryOptions({}));

  // Use cache for instant loading
  const { cachedKeys, isCacheValid, isLoadingCache } = useKeysCache({
    freshData: freshKeys as Key[],
    isLoading,
  });

  // Use cached data if available and valid, otherwise use fresh data
  const keys = (isCacheValid && cachedKeys) || freshKeys;
  const isDataLoading = isLoading && isLoadingCache;

  // Mutations for refresh and recheck
  const { mutate: recheckProvider, isPending: isRecheckingProvider } = useMutation({
    ...trpc.keys.recheck.mutationOptions(),
    onSuccess: () => {
      toast.success("Key recheck started successfully");
      refetch();
    },
    onError: (error) => {
      toast.error(`Failed to start recheck: ${error.message}`);
    },
  });

  const { mutate: recheckAll, isPending: isRecheckingAll } = useMutation({
    ...trpc.keys.recheckAll.mutationOptions(),
    onSuccess: () => {
      toast.success("Recheck started for all providers");
      refetch();
    },
    onError: (error) => {
      toast.error(`Failed to start recheck: ${error.message}`);
    },
  });

  // Type the keys data properly - stable reference
  const typedKeys = useMemo(() => (keys as Key[]) || [], [keys]);

  // Filter keys based on search and filters - memoized for performance
  const filteredKeys = useMemo(() => {
    return typedKeys.filter((key) => {
      const matchesSearch = key.hash.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesProvider = selectedProvider === "all" || key.provider === selectedProvider;
      const matchesStatus = selectedStatus === "all" || key.status === selectedStatus;

      return matchesSearch && matchesProvider && matchesStatus;
    });
  }, [typedKeys, searchQuery, selectedProvider, selectedStatus]);

  // Statistics - memoized for performance
  const { totalKeys, workingKeys, revokedKeys } = useMemo(() => {
    const total = typedKeys.length;
    const working = typedKeys.filter((k) => k.status === "working").length;
    const revoked = typedKeys.filter((k) => k.status === "revoked").length;

    return { totalKeys: total, workingKeys: working, revokedKeys: revoked };
  }, [typedKeys]);

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  }, []);

  const handleProviderChange = useCallback((value: LLM_Providers | "all") => {
    setSelectedProvider(value);
  }, []);

  const handleStatusChange = useCallback((value: KeyStatus) => {
    setSelectedStatus(value);
  }, []);

  const handleClearFilters = useCallback(() => {
    setSearchQuery("");
    setSelectedProvider("all");
    setSelectedStatus("all");
  }, []);

  const handleRefresh = useCallback(() => {
    refetch();
    toast.success("Keys refreshed");
  }, [queryClient]);

  const handleRecheck = useCallback(() => {
    setIsRecheckDialogOpen(true);
  }, []);

  const handleConfirmRecheck = useCallback(() => {
    if (selectedProvider !== "all") {
      recheckProvider({ provider: selectedProvider, status: selectedRecheckStatus });
    } else {
      recheckAll({ status: selectedRecheckStatus });
    }
    setIsRecheckDialogOpen(false);
  }, [selectedProvider, selectedRecheckStatus, recheckProvider, recheckAll]);

  const columns = useMemo(() => getColumns(refetch), [refetch]);

  // Optimized table configuration with stable references
  const table = useReactTable({
    data: filteredKeys,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    onColumnVisibilityChange: setColumnVisibility,
    onPaginationChange: setPagination,
    state: { sorting, columnVisibility, pagination },

    // Performance optimizations
    enableRowSelection: false,
    enableMultiRowSelection: false,
    manualFiltering: false,
    manualSorting: false,
    manualPagination: false,
  });

  return (
    <div className="flex flex-col gap-2 p-2">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 gap-2 md:grid-cols-4">
        <Card className="gap-2 py-4">
          <CardHeader className="flex flex-row items-center justify-between pb-0">
            <CardTitle className="text-lg font-medium">Total Keys</CardTitle>
            <KeyIcon className="text-muted-foreground h-4 w-4" />
          </CardHeader>

          <CardContent>
            <div className="text-2xl font-bold">{totalKeys}</div>
            <p className="text-muted-foreground text-xs">Across all providers</p>
          </CardContent>
        </Card>

        <Card className="gap-2 py-4">
          <CardHeader className="flex flex-row items-center justify-between pb-0">
            <CardTitle className="text-lg font-medium">Working Keys</CardTitle>
            <div className="h-4 w-4 rounded-full bg-green-500" />
          </CardHeader>

          <CardContent>
            <div className="text-2xl font-bold">{workingKeys}</div>
            <p className="text-muted-foreground text-xs">Available for use</p>
          </CardContent>
        </Card>

        <Card className="gap-2 py-4">
          <CardHeader className="flex flex-row items-center justify-between pb-0">
            <CardTitle className="text-lg font-medium">Revoked Keys</CardTitle>
            <div className="h-4 w-4 rounded-full bg-red-500" />
          </CardHeader>

          <CardContent>
            <div className="text-2xl font-bold">{revokedKeys}</div>
            <p className="text-muted-foreground text-xs">Need attention</p>
          </CardContent>
        </Card>

        <Card className="gap-2 py-4">
          <CardHeader className="flex flex-row items-center justify-between pb-0">
            <CardTitle className="text-lg font-medium">Usage Rate</CardTitle>
            <div className="h-4 w-4 rounded-full bg-blue-500" />
          </CardHeader>

          <CardContent>
            <div className="text-2xl font-bold">
              {totalKeys > 0 ? Math.round((workingKeys / totalKeys) * 100) : 0}%
            </div>
            <p className="text-muted-foreground text-xs">Keys working</p>
          </CardContent>
        </Card>
      </div>

      {/* Filter Bar */}
      <div className="flex flex-col items-center gap-2">
        <div className="relative w-full flex-1">
          <SearchIcon className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform" />
          <Input
            placeholder="Search keys by ID..."
            value={searchQuery}
            onChange={handleSearchChange}
            className="pl-10"
          />
        </div>

        <div className="flex w-full items-center justify-between gap-2">
          <div className="flex gap-2">
            {/* Provider Filter */}
            <Select value={selectedProvider} onValueChange={handleProviderChange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Provider" />
              </SelectTrigger>

              <SelectContent>
                <SelectItem value="all">All Providers</SelectItem>

                {LLM_PROVIDERS.map((provider) => (
                  <SelectItem key={provider} value={provider}>
                    <div className="flex items-center gap-2">
                      <Icons.provider provider={provider} className="size-4" />
                      <span>{LLM_PROVIDER_DISPLAY_NAME[provider]}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Status Filter */}
            <Select value={selectedStatus} onValueChange={handleStatusChange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>

              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="working">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-green-500" />
                    <span>Working</span>
                  </div>
                </SelectItem>
                <SelectItem value="out_of_quota">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-orange-500" />
                    <span>Out of Quota</span>
                  </div>
                </SelectItem>
                <SelectItem value="revoked">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-red-500" />
                    <span>Revoked</span>
                  </div>
                </SelectItem>
                <SelectItem value="ratelimited">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-yellow-500" />
                    <span>Rate Limited</span>
                  </div>
                </SelectItem>
                <SelectItem value="disabled">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-gray-500" />
                    <span>Disabled</span>
                  </div>
                </SelectItem>
                <SelectItem value="unknown">
                  <div className="flex items-center gap-2">
                    <div className="h-2 w-2 rounded-full bg-gray-400" />
                    <span>Unknown</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>

            <Button
              data-show={
                searchQuery.length > 0 || selectedProvider !== "all" || selectedStatus !== "all"
              }
              onClick={handleClearFilters}
              className="hidden data-[show=true]:block"
            >
              Clear
            </Button>
          </div>

          <div className="flex items-center gap-2">
            {/* Add Key Button */}
            <Button onClick={() => setIsAddKeyDialogOpen(true)}>
              <PlusIcon className="h-4 w-4" />
              Add Key
            </Button>

            {/* Refresh Button */}
            <Button variant="outline" size="icon" onClick={handleRefresh} title="Refresh">
              <RefreshCwIcon className="h-4 w-4" />
            </Button>

            {/* Recheck Button */}
            <Button
              variant="outline"
              onClick={handleRecheck}
              disabled={isRecheckingProvider || isRecheckingAll}
              title={
                selectedProvider !== "all"
                  ? `Recheck ${LLM_PROVIDER_DISPLAY_NAME[selectedProvider]} keys`
                  : "Recheck all keys"
              }
            >
              {selectedProvider === "all" ? (
                <CheckCircleIcon className="h-4 w-4" />
              ) : (
                <Icons.provider provider={selectedProvider} className="h-4 w-4" />
              )}
              {isRecheckingProvider || isRecheckingAll ? "Rechecking..." : "Recheck"}
            </Button>

            <DataTableViewOptions table={table} />
          </div>
        </div>
      </div>

      {/* Keys Table */}
      {isDataLoading ? (
        <div className="flex h-24 items-center justify-center">
          <div className="text-muted-foreground">Loading API keys...</div>
        </div>
      ) : (
        <DataTable table={table} />
      )}

      {/* Add Key Dialog */}
      <AddKeyDialog open={isAddKeyDialogOpen} onOpenChange={setIsAddKeyDialogOpen} />

      {/* Recheck Confirmation Dialog */}
      <AlertDialog open={isRecheckDialogOpen} onOpenChange={setIsRecheckDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Key Recheck</AlertDialogTitle>
            <AlertDialogDescription>
              This will force a recheck of{" "}
              {selectedProvider !== "all"
                ? `${LLM_PROVIDER_DISPLAY_NAME[selectedProvider]} keys`
                : "all provider keys"}{" "}
              with the selected status. This operation may take some time.
            </AlertDialogDescription>
          </AlertDialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Recheck Status</label>
              <Select
                value={selectedRecheckStatus}
                onValueChange={(value: RecheckStatus) => setSelectedRecheckStatus(value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>

                <SelectContent>
                  <SelectItem value="all">All Keys</SelectItem>
                  <SelectItem value="out_of_quota">
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-orange-500" />
                      <span>Out of Quota Only</span>
                    </div>
                  </SelectItem>

                  <SelectItem value="unknown">
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-gray-400" />
                      <span>Unknown Status Only</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmRecheck}
              disabled={isRecheckingProvider || isRecheckingAll}
            >
              {isRecheckingProvider || isRecheckingAll
                ? "Rechecking..."
                : `Start Recheck ${keys?.filter((k) => (selectedRecheckStatus === "all" || k.status === selectedRecheckStatus) && (selectedProvider === "all" || k.provider === selectedProvider)).length}`}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
