import { HttpError } from "@/shared/key-management/error";

import { type Hand<PERSON> } from "@/lib/middlewares/create-proxy";
import type { ResponseBody } from "@/lib/schemas";

export const handleBlockedResponse: Handler = async ({ ctx, response }) => {
  const contentType = response.headers.get("Content-Type");
  if (!contentType?.startsWith("application/json")) {
    throw new HttpError(500, `Upstream error: Expected JSON response. Got: ${contentType}`);
  }

  ctx.var.afterResponsePromises.push(async () => {
    const responseBody = await response.json();
    ctx.set("fullResponseBody", responseBody);
  });

  ctx.res = new Response(response.body, response);
};
