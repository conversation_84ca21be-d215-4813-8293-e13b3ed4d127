import { type Key } from "@/shared/key-management";
import { BaseKeyProvider } from "@/shared/key-management/base-provider";

import { config } from "@/config";

import { OpenAI<PERSON>eyChecker } from "./checker";

export interface OpenAIKey extends Key {
  readonly provider: "openai";

  metadata: {
    verified: boolean;
    organizationId?: string;
    tier: "tier-1" | "tier-2" | "tier-3" | "tier-4" | "tier-5" | "custom" | "unknown";
  };
}

export class OpenAIKeyProvider extends BaseKeyProvider<OpenAIKey> {
  static override readonly provider = "openai" as const;

  protected override getProviderPrefix(): string {
    return "oai";
  }

  constructor() {
    super(OpenAIKeyProvider.provider);
  }

  public override async init(): Promise<void> {
    const checker = new OpenAIKeyChecker({
      updateFn: this.update.bind(this),
      cloneKeyForOrganizations: this.cloneKeyForOrganizations.bind(this),
    });
    await super.init({ checker });
  }

  public async cloneKeyForOrganizations(key: string, orgs: string[]) {
    const metadata = this.serializeNewKeyMetadataForInsert();

    for (const org of orgs) {
      const hash = this.computeHash(`${key}-${org}`);
      await this.addKey(key, { hash, metadata: { ...metadata, organizationId: org } });
    }
  }

  protected getSeedKeysFromConfig(): string[] {
    return config.keys.openai;
  }

  protected override serializeNewKeyMetadataForInsert(): OpenAIKey["metadata"] {
    return { tier: "unknown", verified: false } satisfies OpenAIKey["metadata"];
  }

  protected override matchesModel(k: OpenAIKey, modelId: string): boolean {
    if (!k.modelIds || (k.modelIds.length > 0 && !k.modelIds.includes(modelId))) return false;
    return true;
  }

  protected override buildUsageStatsModelFamily(modelId: string): string {
    return modelId;
  }
}
